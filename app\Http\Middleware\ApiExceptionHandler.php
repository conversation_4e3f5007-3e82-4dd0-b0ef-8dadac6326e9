<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class ApiExceptionHandler
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): SymfonyResponse
    {
        try {
            return $next($request);
        } catch (\Throwable $exception) {
            // Only handle API routes
            if ($request->is('api/*') || $request->expectsJson()) {
                return $this->handleApiException($exception, $request);
            }

            // Re-throw for non-API routes
            throw $exception;
        }
    }

    /**
     * Handle API exceptions with user-friendly responses
     */
    private function handleApiException(\Throwable $exception, Request $request): Response
    {
        // Log the exception for debugging
        \Log::error('API Exception: ' . $exception->getMessage(), [
            'exception' => $exception,
            'request' => [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => $request->user()?->id,
            ],
        ]);

        return $this->handleException($exception);
    }
}
