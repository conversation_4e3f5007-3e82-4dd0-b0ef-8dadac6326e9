<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\KelasController;
use App\Http\Controllers\Api\PengaturanTahfidzController;
use App\Http\Controllers\Api\SantriKelasController;
use App\Http\Controllers\Api\SetoranController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// ==========================
// 🔑 AUTH SECTION
// ==========================

Route::post('/login', [AuthController::class, 'login']);

Route::middleware('auth:sanctum')->get('/me', function (Request $request) {
    $user = $request->user();
    return response()->json([
        'id' => $user->id,
        'name' => $user->name,
        'email' => $user->email,
        'role' => $user->roleUsers->first()->role->name ?? null,
        'komplek_id' => $user->getIdKomplekAttribute(),
    ]);
});


// ==========================
// 🟢 PUBLIC API
// ==========================


// ==========================
// 🔐 PROTECTED API (REQUIRE LOGIN)
// ==========================

Route::middleware('auth:sanctum')->group(function () {

    // ==========================
    // 📘 KELAS DAN SANTRI KELAS
    // ==========================

    // Kelas dan daftar santri di dalamnya (bisa diakses ustadz dan admin)
    Route::prefix('kelas')->middleware('can.access.absensi')->group(function () {

        // KELAS
        Route::get('/', [KelasController::class, 'index']);
        Route::get('/{id}', [KelasController::class, 'show']);
        Route::post('/', [KelasController::class, 'store']);
        Route::put('/{id}', [KelasController::class, 'update']);
        Route::delete('/{id}', [KelasController::class, 'destroy']);
        Route::get('/{id}/santri', [KelasController::class, 'santri']);

    });

    // SANTRI KELAS

    Route::post('/santri-kelas', [SantriKelasController::class, 'store']); // tambah ke kelas
    Route::post('/santri-kelas/keluar', [SantriKelasController::class, 'keluar']); // keluarkan dari kelas
    Route::get('/santri-kelas/history', [SantriKelasController::class, 'history']);
    Route::get('/santri-kelas/by-kelas/{idKelas}', [SantriKelasController::class, 'getByKelas']);


    // PENGATURAN TAHFIDZ
    Route::post('/pengaturan-tahfidz', [PengaturanTahfidzController::class, 'store']);
    Route::get('/pengaturan-tahfidz/{idKomplek}', [PengaturanTahfidzController::class, 'show']);
    Route::put('/pengaturan-tahfidz/{id}', [PengaturanTahfidzController::class, 'update']);
    Route::delete('/pengaturan-tahfidz/{id}', [PengaturanTahfidzController::class, 'destroy']);

    // Melihat riwayat setoran santri (admin dan ustadz)
    Route::get('/santri/{id}/setoran', [SetoranController::class, 'riwayat'])->middleware('can.access.absensi');

    // Input setoran hanya untuk ustadz
    Route::post('/setoran', [SetoranController::class, 'store'])->middleware('can.manage.absensi');
});
