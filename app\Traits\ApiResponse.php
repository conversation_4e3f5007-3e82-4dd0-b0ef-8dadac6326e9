<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

trait ApiResponse
{
    /**
     * Return a success JSON response
     */
    protected function successResponse(
        string $message = 'Operasi berhasil',
        mixed $data = null,
        int $statusCode = 200,
        array $meta = []
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error JSON response
     */
    protected function errorResponse(
        string $message = 'Terja<PERSON> kesalahan',
        int $statusCode = 500,
        string $errorCode = null,
        mixed $errors = null,
        mixed $debug = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'error_code' => $errorCode ?? $this->getDefaultErrorCode($statusCode),
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        if ($debug !== null && config('app.debug')) {
            $response['debug'] = $debug;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return validation error response
     */
    protected function validationErrorResponse(ValidationException $exception): JsonResponse
    {
        return $this->errorResponse(
            message: 'Data yang dikirim tidak valid',
            statusCode: 422,
            errorCode: 'VALIDATION_ERROR',
            errors: $exception->errors()
        );
    }

    /**
     * Return not found error response
     */
    protected function notFoundResponse(string $resource = 'Data'): JsonResponse
    {
        return $this->errorResponse(
            message: "{$resource} tidak ditemukan",
            statusCode: 404,
            errorCode: 'NOT_FOUND'
        );
    }

    /**
     * Return unauthorized error response
     */
    protected function unauthorizedResponse(string $message = 'Anda tidak memiliki akses'): JsonResponse
    {
        return $this->errorResponse(
            message: $message,
            statusCode: 401,
            errorCode: 'UNAUTHORIZED'
        );
    }

    /**
     * Return forbidden error response
     */
    protected function forbiddenResponse(string $message = 'Akses ditolak'): JsonResponse
    {
        return $this->errorResponse(
            message: $message,
            statusCode: 403,
            errorCode: 'FORBIDDEN'
        );
    }

    /**
     * Return server error response
     */
    protected function serverErrorResponse(
        string $message = 'Terjadi kesalahan pada server',
        \Throwable $exception = null
    ): JsonResponse {
        return $this->errorResponse(
            message: $message,
            statusCode: 500,
            errorCode: 'SERVER_ERROR',
            debug: $exception ? [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ] : null
        );
    }

    /**
     * Return rate limit error response
     */
    protected function rateLimitResponse(string $message = 'Terlalu banyak permintaan'): JsonResponse
    {
        return $this->errorResponse(
            message: $message,
            statusCode: 429,
            errorCode: 'RATE_LIMIT_EXCEEDED'
        );
    }

    /**
     * Return maintenance mode response
     */
    protected function maintenanceResponse(string $message = 'Sistem sedang dalam pemeliharaan'): JsonResponse
    {
        return $this->errorResponse(
            message: $message,
            statusCode: 503,
            errorCode: 'MAINTENANCE_MODE'
        );
    }

    /**
     * Get default error code based on status code
     */
    private function getDefaultErrorCode(int $statusCode): string
    {
        return match ($statusCode) {
            400 => 'BAD_REQUEST',
            401 => 'UNAUTHORIZED',
            403 => 'FORBIDDEN',
            404 => 'NOT_FOUND',
            422 => 'VALIDATION_ERROR',
            429 => 'RATE_LIMIT_EXCEEDED',
            500 => 'SERVER_ERROR',
            503 => 'SERVICE_UNAVAILABLE',
            default => 'UNKNOWN_ERROR',
        };
    }

    /**
     * Handle common exceptions and return appropriate response
     */
    protected function handleException(\Throwable $exception): JsonResponse
    {
        if ($exception instanceof ValidationException) {
            return $this->validationErrorResponse($exception);
        }

        if ($exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            return $this->notFoundResponse();
        }

        if ($exception instanceof \Illuminate\Auth\AuthenticationException) {
            return $this->unauthorizedResponse('Silakan login terlebih dahulu');
        }

        if ($exception instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return $this->forbiddenResponse('Anda tidak memiliki izin untuk melakukan aksi ini');
        }

        if ($exception instanceof \Illuminate\Database\QueryException) {
            return $this->serverErrorResponse('Terjadi kesalahan pada database', $exception);
        }

        return $this->serverErrorResponse('Terjadi kesalahan yang tidak terduga', $exception);
    }
}
