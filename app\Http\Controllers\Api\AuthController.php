<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Throwable;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        try {
            // Validasi input
            $validated = $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);

            // Cari user
            $user = User::where('email', $validated['email'])->first();

            // Cek password
            if (! $user || ! Hash::check($validated['password'], $user->getAuthPassword())) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email atau password salah.',
                ], 401);
            }

            // Buat token
            $token = $user->createToken('flutter_app')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Login berhasil.',
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'komplek_id'
                ],
            ]);

        } catch (ValidationException $e) {
            // Return error validasi dengan struktur rapi
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);

        } catch (Throwable $e) {
            // Tangkap semua error lain (misal DB error, logic error)
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan pada server.',
                'error' => config('app.debug') ? $e->getMessage() : null, // Tampilkan error hanya saat debug
            ], 500);
        }
    }
}
