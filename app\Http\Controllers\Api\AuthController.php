<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Throwable;

class AuthController extends Controller
{
    use ApiResponse;
    public function login(Request $request)
    {
        try {
            // Validasi input
            $validated = $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);

            // Cari user
            $user = User::where('email', $validated['email'])->first();

            // Cek password
            if (! $user || ! Hash::check($validated['password'], $user->getAuthPassword())) {
                return $this->unauthorizedResponse('Email atau password salah');
            }

            // Buat token
            $token = $user->createToken('flutter_app')->plainTextToken;

            return $this->successResponse(
                'Login berhasil',
                [
                    'token' => $token,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->roleUsers->first()->role->name,
                        'komplek_id' => $user->getIdKomplekAttribute(),
                    ],
                ]
            );

        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e);
        } catch (Throwable $e) {
            return $this->serverErrorResponse('Terjadi kesalahan saat login', $e);
        }
    }
}
