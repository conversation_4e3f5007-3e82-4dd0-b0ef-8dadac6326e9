<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CanManageAbsensiKomplek
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $komplekId = $request->route('id_komplek') ?? $request->input('id_komplek');

        if (! $user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        // Superadmin tetap tidak boleh
        if ($user->is_super_admin) {
            return response()->json(['message' => 'Forbidden: Superadmin cannot manage absensi'], 403);
        }

        // Hanya ustadz yang bisa kelola
        $hasAccess = $user->roleUsers()
            ->where('id_komplek', $komplekId)
            ->whereHas('role', function ($query) {
                $query->where('name', 'ustadz');
            })->exists();

        if (! $hasAccess) {
            return response()->json(['message' => 'Forbidden: You are not allowed to manage absensi'], 403);
        }

        return $next($request);
    }
}
